"""
规则详情表压力测试脚本
使用Locust进行高并发压力测试

运行命令：
locust -f tests/performance/locustfile_rule_details.py --host=http://localhost:8000

测试场景：
- 模拟真实用户行为
- 混合读写操作
- 逐步增加负载
- 监控系统性能指标
"""

from locust import HttpUser, task, between
import random
import json
import time
from typing import Dict, Any


class RuleDetailsUser(HttpUser):
    """规则详情表用户行为模拟"""
    
    wait_time = between(1, 3)  # 用户操作间隔1-3秒
    
    def on_start(self):
        """用户开始时的初始化操作"""
        self.headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        self.rule_key = "performance_test_rule"
        self.created_rule_ids = []
        
        # 预热：创建一些测试数据
        self._create_initial_data()
    
    def _create_initial_data(self):
        """创建初始测试数据"""
        for i in range(5):
            rule_data = self._generate_rule_detail(f"init_{self.user_id}_{i}")
            response = self.client.post(
                f"/api/v1/rules/details/{self.rule_key}",
                json=rule_data,
                headers=self.headers,
                name="初始化数据创建"
            )
            if response.status_code == 200:
                self.created_rule_ids.append(rule_data["rule_id"])
    
    def _generate_rule_detail(self, rule_id: str) -> Dict[str, Any]:
        """生成规则明细数据"""
        return {
            "rule_id": rule_id,
            "rule_key": self.rule_key,
            "rule_name": f"压力测试规则{rule_id}",
            "level1": "一级错误类型",
            "level2": "二级错误类型",
            "level3": "三级错误类型",
            "error_reason": f"压力测试错误原因{rule_id}",
            "degree": random.choice(["轻微", "一般", "严重", "致命"]),
            "reference": "压力测试参考资料",
            "detail_position": "测试位置",
            "prompted_fields1": f"TEST{random.randint(1000, 9999)}",
            "type": "压力测试",
            "pos": "测试业务",
            "applicableArea": random.choice(["全国", "北京", "上海", "广州"]),
            "default_use": random.choice(["是", "否"]),
            "start_date": "2025-01-01",
            "end_date": "2025-12-31",
            "status": "ACTIVE",
            "extended_fields": {
                "test_field_1": f"测试值{random.randint(1, 1000)}",
                "test_field_2": random.randint(1, 100),
                "test_field_3": random.choice([True, False])
            }
        }
    
    @task(5)
    def list_rule_details(self):
        """列表查询操作（高频操作）"""
        params = {
            "page": random.randint(1, 10),
            "page_size": random.choice([10, 20, 50])
        }
        
        with self.client.get(
            f"/api/v1/rules/details/{self.rule_key}",
            params=params,
            headers=self.headers,
            name="列表查询",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"API返回失败: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(3)
    def search_rule_details(self):
        """搜索查询操作"""
        search_terms = ["测试", "规则", "错误", "压力", "性能"]
        params = {
            "page": 1,
            "page_size": 20,
            "search": random.choice(search_terms)
        }
        
        with self.client.get(
            f"/api/v1/rules/details/{self.rule_key}",
            params=params,
            headers=self.headers,
            name="搜索查询",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"搜索失败: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(2)
    def create_rule_detail(self):
        """创建规则明细操作"""
        rule_id = f"stress_{self.user_id}_{int(time.time())}_{random.randint(1000, 9999)}"
        rule_data = self._generate_rule_detail(rule_id)
        
        with self.client.post(
            f"/api/v1/rules/details/{self.rule_key}",
            json=rule_data,
            headers=self.headers,
            name="创建规则明细",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.created_rule_ids.append(rule_id)
                    response.success()
                else:
                    response.failure(f"创建失败: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(1)
    def get_rule_detail(self):
        """获取单个规则明细"""
        if not self.created_rule_ids:
            return
        
        # 随机选择一个已创建的规则ID
        rule_id = random.choice(self.created_rule_ids)
        
        with self.client.get(
            f"/api/v1/rules/details/{self.rule_key}/{rule_id}",
            headers=self.headers,
            name="获取单个规则",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"获取失败: {data.get('message')}")
            elif response.status_code == 404:
                # 规则不存在，记录数据一致性问题
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"数据一致性问题：规则 {rule_id} 不存在，可能已被删除")
                
                # 从本地列表中移除不存在的规则ID
                if rule_id in self.created_rule_ids:
                    self.created_rule_ids.remove(rule_id)
                
                # 标记为失败以便监控数据一致性问题
                response.failure(f"数据一致性问题：规则 {rule_id} 不存在")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(1)
    def update_rule_detail(self):
        """更新规则明细操作"""
        if not self.created_rule_ids:
            return
        
        rule_id = random.choice(self.created_rule_ids)
        update_data = {
            "rule_name": f"更新后的规则{rule_id}",
            "degree": random.choice(["轻微", "一般", "严重", "致命"]),
            "error_reason": f"更新后的错误原因{random.randint(1, 1000)}",
            "extended_fields": {
                "updated_field": f"更新值{int(time.time())}",
                "test_field_2": random.randint(1, 100)
            }
        }
        
        with self.client.put(
            f"/api/v1/rules/details/{self.rule_key}/{rule_id}",
            json=update_data,
            headers=self.headers,
            name="更新规则明细",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"更新失败: {data.get('message')}")
            elif response.status_code == 404:
                # 规则不存在，记录数据一致性问题
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"更新操作失败：规则 {rule_id} 不存在，可能已被删除")
                
                # 从本地列表中移除不存在的规则ID
                if rule_id in self.created_rule_ids:
                    self.created_rule_ids.remove(rule_id)
                
                # 标记为失败以便监控数据一致性问题
                response.failure(f"更新失败：规则 {rule_id} 不存在")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(1)
    def batch_create_operation(self):
        """批量创建操作"""
        batch_size = random.randint(5, 20)
        batch_data = []
        
        for i in range(batch_size):
            rule_id = f"batch_{self.user_id}_{int(time.time())}_{i}"
            rule_data = self._generate_rule_detail(rule_id)
            batch_data.append(rule_data)
            self.created_rule_ids.append(rule_id)
        
        with self.client.post(
            f"/api/v1/rules/details/{self.rule_key}/batch",
            json={"items": batch_data},
            headers=self.headers,
            name="批量创建",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    response.success()
                else:
                    response.failure(f"批量创建失败: {data.get('message')}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")
    
    @task(1)
    def excel_template_download(self):
        """Excel模板下载操作"""
        with self.client.get(
            f"/api/v1/rules/{self.rule_key}/template",
            headers=self.headers,
            name="Excel模板下载",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                # 检查是否返回了Excel文件
                content_type = response.headers.get("content-type", "")
                if "excel" in content_type or "spreadsheet" in content_type:
                    response.success()
                else:
                    response.failure(f"返回的不是Excel文件: {content_type}")
            else:
                response.failure(f"HTTP错误: {response.status_code}")


class HighLoadUser(RuleDetailsUser):
    """高负载用户（更频繁的操作）"""
    
    wait_time = between(0.5, 1.5)  # 更短的等待时间
    
    @task(10)
    def rapid_list_queries(self):
        """快速列表查询"""
        self.list_rule_details()
    
    @task(5)
    def rapid_searches(self):
        """快速搜索"""
        self.search_rule_details()


class ReadOnlyUser(HttpUser):
    """只读用户（模拟查看数据的用户）"""
    
    wait_time = between(2, 5)
    
    def on_start(self):
        self.headers = {"X-API-KEY": "a_very_secret_key_for_development"}
        self.rule_key = "performance_test_rule"
    
    @task(8)
    def browse_rules(self):
        """浏览规则列表"""
        params = {
            "page": random.randint(1, 20),
            "page_size": random.choice([10, 20, 50, 100])
        }
        
        self.client.get(
            f"/api/v1/rules/details/{self.rule_key}",
            params=params,
            headers=self.headers,
            name="只读-浏览列表"
        )
    
    @task(2)
    def search_rules(self):
        """搜索规则"""
        search_terms = ["测试", "规则", "错误", "验证", "检查"]
        params = {
            "page": 1,
            "page_size": 50,
            "search": random.choice(search_terms)
        }
        
        self.client.get(
            f"/api/v1/rules/details/{self.rule_key}",
            params=params,
            headers=self.headers,
            name="只读-搜索"
        )


# 自定义事件监听器，用于收集性能指标
from locust import events
import logging

@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """请求事件监听器"""
    if exception:
        logging.error(f"请求失败: {name} - {exception}")
    elif response_time > 1000:  # 超过1秒的请求
        logging.warning(f"慢请求: {name} - {response_time}ms")

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始事件"""
    logging.info("🚀 规则详情表压力测试开始")
    logging.info(f"目标主机: {environment.host}")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束事件"""
    logging.info("✅ 规则详情表压力测试结束")
    
    # 输出测试统计信息
    stats = environment.stats
    logging.info(f"总请求数: {stats.total.num_requests}")
    logging.info(f"失败请求数: {stats.total.num_failures}")
    logging.info(f"平均响应时间: {stats.total.avg_response_time:.2f}ms")
    logging.info(f"最大响应时间: {stats.total.max_response_time}ms")
    logging.info(f"RPS: {stats.total.current_rps:.2f}")
