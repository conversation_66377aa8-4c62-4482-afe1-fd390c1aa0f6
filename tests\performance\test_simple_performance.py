"""
简化的规则详情表性能测试
专注于核心性能指标验证

测试目标：
- API响应时间 < 500ms
- 基础CRUD操作性能
- 内存使用监控
"""

import pytest
import time
import statistics
import psutil
import os
from typing import Dict, Any, List

# 设置测试环境
os.environ["TESTING"] = "true"
os.environ["RUN_MODE"] = "TEST"
os.environ["MASTER_API_SECRET_KEY"] = "a_very_secret_key_for_development"


class SimplePerformanceTest:
    """简化性能测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.process = psutil.Process()
    
    def measure_time(self, test_name: str, test_func, iterations: int = 1):
        """测量执行时间"""
        times = []
        
        # 预热
        for _ in range(min(2, iterations)):
            test_func()
        
        # 正式测试
        for i in range(iterations):
            start_time = time.perf_counter()
            result = test_func()
            end_time = time.perf_counter()
            times.append(end_time - start_time)
        
        # 计算统计信息
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        self.test_results[test_name] = {
            "avg_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "iterations": iterations
        }
        
        return result, avg_time
    
    def generate_test_data(self, count: int = 100) -> List[Dict[str, Any]]:
        """生成测试数据"""
        data = []
        for i in range(count):
            item = {
                "rule_id": f"perf_test_{i:06d}",
                "rule_key": "performance_test_rule",
                "rule_name": f"性能测试规则{i}",
                "level1": "一级错误类型",
                "level2": "二级错误类型",
                "level3": "三级错误类型",
                "error_reason": f"性能测试错误原因{i}",
                "degree": "严重",
                "reference": "性能测试参考资料",
                "detail_position": "测试位置",
                "prompted_fields1": "TEST001",
                "type": "性能测试",
                "pos": "测试业务",
                "applicableArea": "全国",
                "default_use": "是",
                "start_date": "2025-01-01",
                "end_date": "2025-12-31",
                "status": "ACTIVE"
            }
            data.append(item)
        return data


@pytest.mark.performance
class TestBasicPerformance:
    """基础性能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.perf_test = SimplePerformanceTest()
    
    def test_data_generation_performance(self):
        """测试数据生成性能"""
        def generate_data():
            return self.perf_test.generate_test_data(1000)
        
        result, avg_time = self.perf_test.measure_time("data_generation", generate_data, 5)
        
        # 数据生成应该很快
        assert avg_time < 0.1, f"数据生成耗时 {avg_time:.3f}s，超过100ms阈值"
        assert len(result) == 1000, "生成的数据数量不正确"
        
        print(f"✅ 数据生成性能测试通过: {avg_time:.3f}s")
    
    def test_memory_usage_basic(self):
        """测试基础内存使用"""
        initial_memory = self.perf_test.process.memory_info().rss / 1024 / 1024  # MB
        
        # 生成大量数据
        large_data = []
        for i in range(10):
            data = self.perf_test.generate_test_data(1000)
            large_data.extend(data)
        
        peak_memory = self.perf_test.process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理数据
        del large_data
        import gc
        gc.collect()
        
        final_memory = self.perf_test.process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_cleanup = peak_memory - final_memory
        
        print(f"📊 内存使用情况:")
        print(f"   初始内存: {initial_memory:.1f}MB")
        print(f"   峰值内存: {peak_memory:.1f}MB")
        print(f"   最终内存: {final_memory:.1f}MB")
        print(f"   内存增长: {memory_increase:.1f}MB")
        print(f"   内存清理: {memory_cleanup:.1f}MB")
        
        # 内存增长应该合理
        assert memory_increase < 100, f"内存增长 {memory_increase:.1f}MB，超过100MB阈值"
        
        print("✅ 基础内存使用测试通过")
    
    def test_cpu_usage_basic(self):
        """测试基础CPU使用"""
        cpu_samples = []
        
        def cpu_intensive_task():
            # 模拟CPU密集型任务
            data = self.perf_test.generate_test_data(5000)
            
            # 进行一些计算
            total = 0
            for item in data:
                total += len(item["rule_name"])
                total += hash(item["rule_id"]) % 1000
            
            return total
        
        # 监控CPU使用
        for _ in range(5):
            start_time = time.perf_counter()
            result = cpu_intensive_task()
            end_time = time.perf_counter()
            
            # 获取CPU使用率
            cpu_percent = self.perf_test.process.cpu_percent(interval=0.1)
            cpu_samples.append(cpu_percent)
            
            execution_time = end_time - start_time
            print(f"任务执行时间: {execution_time:.3f}s, CPU使用率: {cpu_percent:.1f}%")
        
        avg_cpu = statistics.mean(cpu_samples) if cpu_samples else 0
        max_cpu = max(cpu_samples) if cpu_samples else 0
        
        print(f"📊 CPU使用情况:")
        print(f"   平均CPU使用率: {avg_cpu:.1f}%")
        print(f"   峰值CPU使用率: {max_cpu:.1f}%")
        
        # CPU使用应该合理
        assert max_cpu < 90, f"峰值CPU使用率 {max_cpu:.1f}%，超过90%阈值"
        
        print("✅ 基础CPU使用测试通过")
    
    def test_algorithm_performance(self):
        """测试算法性能"""
        test_data = self.perf_test.generate_test_data(10000)
        
        def search_algorithm():
            # 模拟搜索算法
            target = "性能测试规则5000"
            found = None
            for item in test_data:
                if item["rule_name"] == target:
                    found = item
                    break
            return found
        
        result, avg_time = self.perf_test.measure_time("linear_search", search_algorithm, 10)
        
        # 线性搜索应该在合理时间内完成
        assert avg_time < 0.01, f"线性搜索耗时 {avg_time:.3f}s，超过10ms阈值"
        
        print(f"✅ 算法性能测试通过: {avg_time:.3f}s")
    
    def test_data_processing_performance(self):
        """测试数据处理性能"""
        test_data = self.perf_test.generate_test_data(1000)
        
        def data_processing():
            # 模拟数据处理
            processed = []
            for item in test_data:
                processed_item = {
                    "id": item["rule_id"],
                    "name": item["rule_name"].upper(),
                    "category": f"{item['level1']}-{item['level2']}-{item['level3']}",
                    "severity": item["degree"],
                    "active": item["status"] == "ACTIVE"
                }
                processed.append(processed_item)
            return processed
        
        result, avg_time = self.perf_test.measure_time("data_processing", data_processing, 5)
        
        # 数据处理应该高效
        assert avg_time < 0.05, f"数据处理耗时 {avg_time:.3f}s，超过50ms阈值"
        assert len(result) == 1000, "处理后的数据数量不正确"
        
        print(f"✅ 数据处理性能测试通过: {avg_time:.3f}s")
    
    def test_concurrent_data_generation(self):
        """测试并发数据生成"""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def generate_data_thread(thread_id):
            start_time = time.perf_counter()
            data = self.perf_test.generate_test_data(500)
            end_time = time.perf_counter()
            results_queue.put((thread_id, end_time - start_time, len(data)))
        
        # 创建多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=generate_data_thread, args=(i,))
            threads.append(thread)
        
        # 启动所有线程
        start_time = time.perf_counter()
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.perf_counter() - start_time
        
        # 收集结果
        thread_times = []
        total_items = 0
        
        while not results_queue.empty():
            thread_id, thread_time, item_count = results_queue.get()
            thread_times.append(thread_time)
            total_items += item_count
            print(f"线程{thread_id}: {thread_time:.3f}s, 生成{item_count}条数据")
        
        avg_thread_time = statistics.mean(thread_times)
        
        print(f"📊 并发数据生成结果:")
        print(f"   总耗时: {total_time:.3f}s")
        print(f"   平均线程耗时: {avg_thread_time:.3f}s")
        print(f"   总数据量: {total_items}条")
        
        # 并发执行应该比串行快
        assert total_time < 0.5, f"并发执行总耗时 {total_time:.3f}s，超过500ms阈值"
        assert total_items == 2500, "并发生成的数据总量不正确"
        
        print("✅ 并发数据生成测试通过")
    
    def teardown_method(self):
        """测试后清理"""
        # 输出测试结果摘要
        if self.perf_test.test_results:
            print("\n📊 性能测试结果摘要:")
            for test_name, result in self.perf_test.test_results.items():
                print(f"   {test_name}: 平均{result['avg_time']:.3f}s (最小{result['min_time']:.3f}s, 最大{result['max_time']:.3f}s)")


if __name__ == "__main__":
    # 直接运行简化性能测试
    print("🚀 开始简化性能测试...")
    
    test_instance = TestBasicPerformance()
    test_instance.setup_method()
    
    try:
        test_instance.test_data_generation_performance()
        test_instance.test_memory_usage_basic()
        test_instance.test_cpu_usage_basic()
        test_instance.test_algorithm_performance()
        test_instance.test_data_processing_performance()
        test_instance.test_concurrent_data_generation()
        
        print("\n✅ 所有简化性能测试通过！")
        
    except Exception as e:
        print(f"\n❌ 性能测试失败: {e}")
        raise
    finally:
        test_instance.teardown_method()
