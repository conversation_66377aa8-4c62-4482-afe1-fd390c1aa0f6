"""
性能测试修复验证脚本（简化版）
验证所有修复的问题是否正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 设置测试环境
os.environ["TESTING"] = "true"
os.environ["RUN_MODE"] = "TEST"
os.environ["MASTER_API_SECRET_KEY"] = "a_very_secret_key_for_development"

def validate_configuration_fix():
    """验证配置修复：应用配置灵活性"""
    print("验证修复1：应用配置灵活性")
    
    try:
        # 设置自定义环境变量
        os.environ["PERFORMANCE_TEST_APP_TITLE"] = "自定义测试应用"
        os.environ["PERFORMANCE_TEST_APP_VERSION"] = "2.0.0"
        
        from tests.performance.test_rule_details_performance import PerformanceTestBase
        test_base = PerformanceTestBase()
        
        # 验证配置生效
        assert test_base.app.title == "自定义测试应用"
        assert test_base.app.version == "2.0.0"
        
        print("  [通过] 应用配置灵活性修复正常工作")
        return True
        
    except Exception as e:
        print(f"  [失败] 应用配置灵活性修复失败: {e}")
        return False
    finally:
        # 清理环境变量
        os.environ.pop("PERFORMANCE_TEST_APP_TITLE", None)
        os.environ.pop("PERFORMANCE_TEST_APP_VERSION", None)

def validate_resource_management_fix():
    """验证资源管理修复"""
    print("验证修复2：资源管理和清理机制")
    
    try:
        from tests.performance.test_rule_details_performance import PerformanceTestBase
        test_base = PerformanceTestBase()
        
        # 验证资源管理属性
        assert hasattr(test_base, 'cleanup_callbacks')
        assert hasattr(test_base, 'created_resources')
        
        # 测试注册功能
        def mock_cleanup():
            pass
        
        test_base.register_cleanup_callback(mock_cleanup)
        test_base.register_resource("test_data", "test_id", {"rule_key": "test"})
        
        assert len(test_base.cleanup_callbacks) >= 1
        assert len(test_base.created_resources) >= 1
        
        print("  [通过] 资源管理和清理机制修复正常工作")
        return True
        
    except Exception as e:
        print(f"  [失败] 资源管理修复失败: {e}")
        return False

def validate_regression_detection_fix():
    """验证回归检测修复"""
    print("验证修复3：性能回归检测功能")
    
    try:
        from tests.performance.performance_regression_detector import PerformanceRegressionDetector
        
        # 创建检测器并测试基本功能
        detector = PerformanceRegressionDetector("test_baseline.json")
        detector.add_metrics_batch({
            "api_response_time": 120,
            "memory_usage": 100,
        })
        
        # 验证功能
        assert len(detector.current_metrics) == 2
        detector.set_baseline(save_to_file=False)
        assert len(detector.baseline_metrics) == 2
        
        print("  [通过] 性能回归检测功能修复正常工作")
        return True
        
    except Exception as e:
        print(f"  [失败] 性能回归检测修复失败: {e}")
        return False
    finally:
        try:
            os.remove("test_baseline.json")
        except:
            pass

def validate_data_manager_fix():
    """验证数据管理器修复"""
    print("验证修复4：测试数据管理器")
    
    try:
        from tests.performance.test_data_manager import TestDataManager
        
        # 测试数据管理器功能
        manager = TestDataManager(cleanup_on_exit=False)
        
        # 生成测试数据
        data = manager.generate_rule_details(10, "test_rule")
        assert len(data) == 10
        assert all(item["rule_key"] == "test_rule" for item in data)
        
        # 测试数据集生成
        datasets = manager.create_test_datasets({"small": 5})
        assert "small_dataset" in datasets
        assert len(datasets["small_dataset"]) == 5
        
        print("  [通过] 测试数据管理器修复正常工作")
        return True
        
    except Exception as e:
        print(f"  [失败] 测试数据管理器修复失败: {e}")
        return False

def validate_concurrent_stability_fix():
    """验证并发稳定性修复"""
    print("验证修复5：并发测试稳定性")
    
    try:
        import threading
        import queue
        
        # 模拟并发测试
        results = queue.Queue()
        
        def mock_concurrent_request():
            # 模拟成功的并发请求
            results.put({
                "status": "success",
                "response_time": 0.1,
                "thread_id": threading.current_thread().ident
            })
        
        # 创建并运行线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=mock_concurrent_request, name=f"Test-{i}")
            threads.append(thread)
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join(timeout=5)
            assert not thread.is_alive(), f"线程 {thread.name} 超时"
        
        # 验证结果
        success_count = 0
        while not results.empty():
            result = results.get()
            if result["status"] == "success":
                success_count += 1
        
        assert success_count == 5
        
        print("  [通过] 并发测试稳定性修复正常工作")
        return True
        
    except Exception as e:
        print(f"  [失败] 并发测试稳定性修复失败: {e}")
        return False

def main():
    """主验证函数"""
    print("性能测试修复验证")
    print("=" * 50)
    
    validators = [
        validate_configuration_fix,
        validate_resource_management_fix,
        validate_regression_detection_fix,
        validate_data_manager_fix,
        validate_concurrent_stability_fix,
    ]
    
    results = []
    for validator in validators:
        try:
            result = validator()
            results.append(result)
        except Exception as e:
            print(f"  [异常] {validator.__name__}: {e}")
            results.append(False)
        print()
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print("验证结果总结:")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("所有修复验证通过！")
        return True
    else:
        print(f"{total - passed} 个修复需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)