/**
 * 规则详情表前端性能测试
 * 测试组件渲染性能、API调用性能、内存使用等
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { ElTable, ElPagination, ElButton } from 'element-plus'
import RuleDetailsTable from '@/components/business/RuleDetailsTable.vue'
import RuleDetailForm from '@/components/business/RuleDetailForm.vue'
import { useRuleDetailsStore } from '@/stores/ruleDetails'

// 性能测试工具类
class PerformanceTestUtils {
  constructor() {
    this.measurements = {}
  }

  // 测量函数执行时间
  async measureTime(name, fn) {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    const duration = end - start

    this.measurements[name] = {
      duration,
      timestamp: new Date().toISOString()
    }

    return { result, duration }
  }

  // 测量内存使用
  measureMemory(name) {
    if (performance.memory) {
      this.measurements[`${name}_memory`] = {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
        timestamp: new Date().toISOString()
      }
    }
  }

  // 生成测试数据
  generateTestData(count = 1000) {
    const data = []
    for (let i = 0; i < count; i++) {
      data.push({
        id: i + 1,
        rule_id: `perf_test_${i.toString().padStart(6, '0')}`,
        rule_key: 'performance_test_rule',
        rule_name: `性能测试规则${i}`,
        level1: '一级错误类型',
        level2: '二级错误类型',
        level3: '三级错误类型',
        error_reason: `性能测试错误原因${i}`,
        degree: ['轻微', '一般', '严重', '致命'][i % 4],
        reference: '性能测试参考资料',
        detail_position: '测试位置',
        prompted_fields1: `TEST${(i + 1000).toString()}`,
        type: '性能测试',
        pos: '测试业务',
        applicableArea: ['全国', '北京', '上海', '广州'][i % 4],
        default_use: i % 2 === 0 ? '是' : '否',
        start_date: '2025-01-01',
        end_date: '2025-12-31',
        status: 'ACTIVE',
        extended_fields: {
          test_field_1: `测试值${i}`,
          test_field_2: i % 100,
          test_field_3: i % 2 === 0
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    return data
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      measurements: this.measurements,
      summary: this.generateSummary()
    }
  }

  generateSummary() {
    const timeMeasurements = Object.entries(this.measurements)
      .filter(([key]) => !key.includes('_memory'))
      .map(([key, value]) => ({ name: key, duration: value.duration }))

    const memoryMeasurements = Object.entries(this.measurements)
      .filter(([key]) => key.includes('_memory'))
      .map(([key, value]) => ({ name: key, ...value }))

    return {
      averageRenderTime: timeMeasurements.length > 0
        ? timeMeasurements.reduce((sum, m) => sum + m.duration, 0) / timeMeasurements.length
        : 0,
      slowestOperation: timeMeasurements.length > 0
        ? timeMeasurements.reduce((max, m) => m.duration > max.duration ? m : max)
        : null,
      memoryUsage: memoryMeasurements
    }
  }
}

describe('规则详情表前端性能测试', () => {
  let performanceUtils
  let pinia
  let store

  beforeEach(() => {
    performanceUtils = new PerformanceTestUtils()
    pinia = createPinia()
    setActivePinia(pinia)
    store = useRuleDetailsStore()

    // Mock API 响应
    global.fetch = vi.fn()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('组件渲染性能测试', () => {
    it('应该在合理时间内渲染小数据集（100条记录）', async () => {
      const testData = performanceUtils.generateTestData(100)

      // 模拟API响应
      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 100
          }
        })
      })

      performanceUtils.measureMemory('before_render_small')

      const { duration } = await performanceUtils.measureTime('render_small_dataset', async () => {
        const wrapper = mount(RuleDetailsTable, {
          global: {
            plugins: [pinia],
            components: {
              ElTable,
              ElPagination,
              ElButton
            }
          },
          props: {
            ruleKey: 'performance_test_rule'
          }
        })

        // 等待数据加载
        await store.fetchRuleDetails('performance_test_rule')
        await wrapper.vm.$nextTick()

        return wrapper
      })

      performanceUtils.measureMemory('after_render_small')

      // 性能断言
      expect(duration).toBeLessThan(500) // 小数据集渲染应在500ms内完成
      console.log(`✅ 小数据集渲染性能: ${duration.toFixed(2)}ms`)
    })

    it('应该在合理时间内渲染中等数据集（1000条记录）', async () => {
      const testData = performanceUtils.generateTestData(1000)

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 1000
          }
        })
      })

      performanceUtils.measureMemory('before_render_medium')

      const { duration } = await performanceUtils.measureTime('render_medium_dataset', async () => {
        const wrapper = mount(RuleDetailsTable, {
          global: {
            plugins: [pinia],
            components: {
              ElTable,
              ElPagination,
              ElButton
            }
          },
          props: {
            ruleKey: 'performance_test_rule'
          }
        })

        await store.fetchRuleDetails('performance_test_rule')
        await wrapper.vm.$nextTick()

        return wrapper
      })

      performanceUtils.measureMemory('after_render_medium')

      // 性能断言
      expect(duration).toBeLessThan(2000) // 中等数据集渲染应在2秒内完成
      console.log(`✅ 中等数据集渲染性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效处理表格滚动操作', async () => {
      const testData = performanceUtils.generateTestData(1000)

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 1000
          }
        })
      })

      const wrapper = mount(RuleDetailsTable, {
        global: {
          plugins: [pinia],
          components: {
            ElTable,
            ElPagination,
            ElButton
          }
        },
        props: {
          ruleKey: 'performance_test_rule'
        }
      })

      await store.fetchRuleDetails('performance_test_rule')
      await wrapper.vm.$nextTick()

      // 模拟滚动操作
      const { duration } = await performanceUtils.measureTime('table_scroll', async () => {
        const tableElement = wrapper.find('.el-table__body-wrapper')

        // 模拟多次滚动
        for (let i = 0; i < 10; i++) {
          await tableElement.trigger('scroll', {
            target: { scrollTop: i * 100 }
          })
          await wrapper.vm.$nextTick()
        }
      })

      // 滚动操作应该很快
      expect(duration).toBeLessThan(1000)
      console.log(`✅ 表格滚动性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('API调用性能测试', () => {
    it('应该快速完成数据获取', async () => {
      const testData = performanceUtils.generateTestData(100)

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 100
          }
        })
      })

      const { duration } = await performanceUtils.measureTime('api_fetch', async () => {
        return await store.fetchRuleDetails('performance_test_rule', {
          page: 1,
          page_size: 100
        })
      })

      // API调用应在200ms内完成（模拟环境）
      expect(duration).toBeLessThan(200)
      console.log(`✅ API调用性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效处理批量创建操作', async () => {
      const testData = performanceUtils.generateTestData(50)

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            created_count: testData.length,
            failed_count: 0
          }
        })
      })

      const { duration } = await performanceUtils.measureTime('api_batch_create', async () => {
        return await store.batchCreateRuleDetails('performance_test_rule', testData)
      })

      // 批量创建应在500ms内完成（模拟环境）
      expect(duration).toBeLessThan(500)
      console.log(`✅ 批量创建性能: ${duration.toFixed(2)}ms`)
    })

    it('应该快速响应搜索操作', async () => {
      const testData = performanceUtils.generateTestData(20)

      global.fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 20
          }
        })
      })

      const { duration } = await performanceUtils.measureTime('api_search', async () => {
        return await store.searchRuleDetails('performance_test_rule', {
          search: '性能测试',
          page: 1,
          page_size: 20
        })
      })

      // 搜索操作应在300ms内完成
      expect(duration).toBeLessThan(300)
      console.log(`✅ 搜索操作性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('表单操作性能测试', () => {
    it('应该快速渲染规则详情表单', async () => {
      const { duration } = await performanceUtils.measureTime('form_render', async () => {
        const wrapper = mount(RuleDetailForm, {
          global: {
            plugins: [pinia]
          },
          props: {
            ruleKey: 'performance_test_rule',
            mode: 'create'
          }
        })

        await wrapper.vm.$nextTick()
        return wrapper
      })

      // 表单渲染应在200ms内完成
      expect(duration).toBeLessThan(200)
      console.log(`✅ 表单渲染性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效处理表单验证', async () => {
      const wrapper = mount(RuleDetailForm, {
        global: {
          plugins: [pinia]
        },
        props: {
          ruleKey: 'performance_test_rule',
          mode: 'create'
        }
      })

      await wrapper.vm.$nextTick()

      const { duration } = await performanceUtils.measureTime('form_validation', async () => {
        // 模拟表单输入
        const formData = {
          rule_id: 'test_rule_001',
          rule_name: '测试规则',
          level1: '一级错误',
          level2: '二级错误',
          level3: '三级错误',
          error_reason: '测试错误原因',
          degree: '严重'
        }

        // 触发表单验证
        await wrapper.vm.validateForm(formData)
      })

      // 表单验证应在100ms内完成
      expect(duration).toBeLessThan(100)
      console.log(`✅ 表单验证性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('内存使用测试', () => {
    it('应该有效管理内存使用', async () => {
      if (!performance.memory) {
        console.log('⚠️ 浏览器不支持内存监控，跳过内存测试')
        return
      }

      performanceUtils.measureMemory('initial')

      // 创建大量组件实例
      const wrappers = []
      for (let i = 0; i < 10; i++) {
        const testData = performanceUtils.generateTestData(100)

        global.fetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              items: testData,
              total: testData.length,
              page: 1,
              page_size: 100
            }
          })
        })

        const wrapper = mount(RuleDetailsTable, {
          global: {
            plugins: [pinia],
            components: {
              ElTable,
              ElPagination,
              ElButton
            }
          },
          props: {
            ruleKey: `performance_test_rule_${i}`
          }
        })

        wrappers.push(wrapper)
      }

      performanceUtils.measureMemory('after_creation')

      // 销毁组件
      wrappers.forEach(wrapper => wrapper.unmount())

      // 强制垃圾回收（如果支持）
      if (global.gc) {
        global.gc()
      }

      performanceUtils.measureMemory('after_cleanup')

      const report = performanceUtils.getPerformanceReport()
      console.log('📊 内存使用报告:', report.summary.memoryUsage)

      // 验证内存没有显著泄漏
      const initial = report.summary.memoryUsage.find(m => m.name === 'initial_memory')
      const afterCleanup = report.summary.memoryUsage.find(m => m.name === 'after_cleanup_memory')

      if (initial && afterCleanup) {
        const memoryIncrease = afterCleanup.usedJSHeapSize - initial.usedJSHeapSize
        const memoryIncreasePercent = (memoryIncrease / initial.usedJSHeapSize) * 100

        // 内存增长不应超过50%
        expect(memoryIncreasePercent).toBeLessThan(50)
        console.log(`✅ 内存使用测试通过，增长: ${memoryIncreasePercent.toFixed(2)}%`)
      }
    })
  })

  describe('并发操作性能测试', () => {
    it('应该处理并发API调用', async () => {
      const promises = []

      // 模拟10个并发API调用
      for (let i = 0; i < 10; i++) {
        global.fetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: {
              items: performanceUtils.generateTestData(50),
              total: 50,
              page: 1,
              page_size: 50
            }
          })
        })

        promises.push(
          store.fetchRuleDetails(`performance_test_rule_${i}`, {
            page: 1,
            page_size: 50
          })
        )
      }

      const { duration } = await performanceUtils.measureTime('concurrent_api_calls', async () => {
        return await Promise.all(promises)
      })

      // 并发调用应在1秒内完成
      expect(duration).toBeLessThan(1000)
      console.log(`✅ 并发API调用性能: ${duration.toFixed(2)}ms`)
    })
  })

  afterAll(() => {
    // 输出完整的性能报告
    const report = performanceUtils.getPerformanceReport()
    console.log('\n📊 前端性能测试完整报告:')
    console.log('平均渲染时间:', report.summary.averageRenderTime.toFixed(2), 'ms')
    if (report.summary.slowestOperation) {
      console.log('最慢操作:', report.summary.slowestOperation.name, '-', report.summary.slowestOperation.duration.toFixed(2), 'ms')
    }
    console.log('详细测量数据:', JSON.stringify(report.measurements, null, 2))
  })
})
