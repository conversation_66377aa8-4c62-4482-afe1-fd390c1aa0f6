"""
性能回归检测器
用于检测性能测试中的性能退化和异常情况
"""

import json
import os
import time
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime, timedelta


class RegressionSeverity(Enum):
    """回归严重程度"""
    MINOR = "轻微"       # 5-15% 性能下降
    MODERATE = "中等"    # 15-30% 性能下降
    SEVERE = "严重"      # 30-50% 性能下降
    CRITICAL = "致命"    # >50% 性能下降


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: float
    test_context: Dict[str, Any] = None


@dataclass
class RegressionResult:
    """回归检测结果"""
    metric_name: str
    current_value: float
    baseline_value: float
    regression_percent: float
    severity: RegressionSeverity
    is_regression: bool
    message: str
    recommendations: List[str] = None


class PerformanceRegressionDetector:
    """性能回归检测器"""
    
    def __init__(self, baseline_file: str = "performance_baseline.json"):
        self.baseline_file = baseline_file
        self.baseline_metrics = {}
        self.current_metrics = {}
        self.regression_thresholds = {
            RegressionSeverity.MINOR: 0.05,      # 5%
            RegressionSeverity.MODERATE: 0.15,   # 15%
            RegressionSeverity.SEVERE: 0.30,     # 30%
            RegressionSeverity.CRITICAL: 0.50    # 50%
        }
        
        self._load_baseline()
    
    def _load_baseline(self):
        """加载基准性能数据"""
        if os.path.exists(self.baseline_file):
            try:
                with open(self.baseline_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.baseline_metrics = data.get('metrics', {})
                print(f"已加载基准数据：{len(self.baseline_metrics)} 个指标")
            except Exception as e:
                print(f"警告：加载基准数据失败: {e}")
                self.baseline_metrics = {}
        else:
            print("未找到基准数据文件，将创建新的基准")
    
    def add_metric(self, name: str, value: float, unit: str = "ms", 
                   test_context: Dict[str, Any] = None):
        """添加性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=time.time(),
            test_context=test_context or {}
        )
        self.current_metrics[name] = metric
    
    def add_metrics_batch(self, metrics: Dict[str, float], unit: str = "ms"):
        """批量添加性能指标"""
        for name, value in metrics.items():
            self.add_metric(name, value, unit)
    
    def set_baseline(self, save_to_file: bool = True):
        """设置当前指标为基准"""
        baseline_data = {}
        for name, metric in self.current_metrics.items():
            baseline_data[name] = {
                "value": metric.value,
                "unit": metric.unit,
                "timestamp": metric.timestamp,
                "test_context": metric.test_context
            }
        
        self.baseline_metrics = baseline_data
        
        if save_to_file:
            self._save_baseline()
        
        print(f"已更新基准数据：{len(baseline_data)} 个指标")
    
    def _save_baseline(self):
        """保存基准数据到文件"""
        try:
            baseline_info = {
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
                "description": "性能测试基准数据",
                "metrics": self.baseline_metrics
            }
            
            with open(self.baseline_file, 'w', encoding='utf-8') as f:
                json.dump(baseline_info, f, ensure_ascii=False, indent=2)
            
            print(f"基准数据已保存到: {self.baseline_file}")
        
        except Exception as e:
            print(f"保存基准数据失败: {e}")
    
    def detect_regression(self, metric_name: str = None) -> List[RegressionResult]:
        """检测性能回归"""
        results = []
        
        # 如果指定了指标名称，只检测该指标
        metrics_to_check = [metric_name] if metric_name else self.current_metrics.keys()
        
        for name in metrics_to_check:
            if name not in self.current_metrics:
                continue
                
            current_metric = self.current_metrics[name]
            
            if name not in self.baseline_metrics:
                print(f"警告：指标 {name} 没有基准数据，跳过检测")
                continue
            
            baseline_value = self.baseline_metrics[name]["value"]
            current_value = current_metric.value
            
            result = self._analyze_metric_regression(name, current_value, baseline_value)
            results.append(result)
        
        return results
    
    def _analyze_metric_regression(self, metric_name: str, current_value: float, 
                                  baseline_value: float) -> RegressionResult:
        """分析单个指标的回归情况"""
        if baseline_value == 0:
            regression_percent = 0.0
        else:
            regression_percent = (current_value - baseline_value) / baseline_value
        
        # 确定严重程度
        severity = self._determine_severity(abs(regression_percent))
        
        # 判断是否为回归（性能下降）
        is_regression = regression_percent > self.regression_thresholds[RegressionSeverity.MINOR]
        
        # 生成消息和建议
        message, recommendations = self._generate_regression_message(
            metric_name, current_value, baseline_value, regression_percent, severity, is_regression
        )
        
        return RegressionResult(
            metric_name=metric_name,
            current_value=current_value,
            baseline_value=baseline_value,
            regression_percent=regression_percent,
            severity=severity,
            is_regression=is_regression,
            message=message,
            recommendations=recommendations
        )
    
    def _determine_severity(self, regression_percent: float) -> RegressionSeverity:
        """确定回归严重程度"""
        if regression_percent >= self.regression_thresholds[RegressionSeverity.CRITICAL]:
            return RegressionSeverity.CRITICAL
        elif regression_percent >= self.regression_thresholds[RegressionSeverity.SEVERE]:
            return RegressionSeverity.SEVERE
        elif regression_percent >= self.regression_thresholds[RegressionSeverity.MODERATE]:
            return RegressionSeverity.MODERATE
        else:
            return RegressionSeverity.MINOR
    
    def _generate_regression_message(self, metric_name: str, current_value: float, 
                                   baseline_value: float, regression_percent: float,
                                   severity: RegressionSeverity, is_regression: bool) -> Tuple[str, List[str]]:
        """生成回归消息和建议"""
        if regression_percent > 0:
            direction = "下降"
            icon = "下降"
        else:
            direction = "提升"
            icon = "提升"
        
        message = (f"{icon} {metric_name}: {current_value:.3f} vs {baseline_value:.3f} "
                  f"(性能{direction} {abs(regression_percent):.2%})")
        
        recommendations = []
        
        if is_regression:
            if severity == RegressionSeverity.CRITICAL:
                recommendations.extend([
                    "立即停止部署，进行紧急性能调查",
                    "检查是否有重大代码变更或配置变更",
                    "考虑回滚到上一个稳定版本"
                ])
            elif severity == RegressionSeverity.SEVERE:
                recommendations.extend([
                    "需要立即进行性能优化",
                    "分析性能瓶颈，重点检查数据库查询和算法效率",
                    "增加监控和告警"
                ])
            elif severity == RegressionSeverity.MODERATE:
                recommendations.extend([
                    "计划性能优化工作",
                    "分析性能趋势，制定优化策略"
                ])
            else:
                recommendations.extend([
                    "持续监控性能变化",
                    "记录性能变化原因"
                ])
        else:
            recommendations.append("性能表现良好，继续保持")
        
        return message, recommendations
    
    def generate_report(self, results: List[RegressionResult] = None) -> Dict[str, Any]:
        """生成回归检测报告"""
        if results is None:
            results = self.detect_regression()
        
        # 分类统计
        regressions = [r for r in results if r.is_regression]
        improvements = [r for r in results if r.regression_percent < -0.05]  # 性能提升超过5%
        stable = [r for r in results if not r.is_regression and r.regression_percent >= -0.05]
        
        # 按严重程度分组
        severity_groups = {}
        for severity in RegressionSeverity:
            severity_groups[severity.value] = [r for r in regressions if r.severity == severity]
        
        report = {
            "检测时间": datetime.now().isoformat(),
            "总体概况": {
                "检测指标数": len(results),
                "性能回归数": len(regressions),
                "性能提升数": len(improvements),
                "性能稳定数": len(stable)
            },
            "回归统计": {
                severity.value: len(severity_groups[severity.value]) 
                for severity in RegressionSeverity
            },
            "详细结果": [asdict(result) for result in results],
            "建议措施": self._generate_overall_recommendations(regressions)
        }
        
        return report
    
    def _generate_overall_recommendations(self, regressions: List[RegressionResult]) -> List[str]:
        """生成整体建议"""
        if not regressions:
            return ["所有性能指标正常，无需特别措施"]
        
        recommendations = []
        critical_count = sum(1 for r in regressions if r.severity == RegressionSeverity.CRITICAL)
        severe_count = sum(1 for r in regressions if r.severity == RegressionSeverity.SEVERE)
        
        if critical_count > 0:
            recommendations.extend([
                f"发现 {critical_count} 个致命性能回归，建议立即暂停发布",
                "启动紧急响应流程，组织技术团队进行问题排查",
                "准备回滚方案"
            ])
        elif severe_count > 0:
            recommendations.extend([
                f"发现 {severe_count} 个严重性能回归，需要优先处理",
                "制定性能优化计划，分配专门资源",
                "增强性能监控和告警"
            ])
        elif len(regressions) > 3:
            recommendations.extend([
                f"发现 {len(regressions)} 个性能回归，建议进行系统性能审查",
                "分析性能趋势，识别根本原因"
            ])
        else:
            recommendations.append("关注性能变化，适当进行优化调整")
        
        return recommendations
    
    def print_report(self, results: List[RegressionResult] = None):
        """打印回归检测报告"""
        report = self.generate_report(results)
        
        print("\n" + "="*60)
        print("性能回归检测报告")
        print("="*60)
        
        # 总体概况
        overview = report["总体概况"]
        print(f"\n总体概况:")
        print(f"   检测指标数: {overview['检测指标数']}")
        print(f"   性能回归数: {overview['性能回归数']} [失败]")
        print(f"   性能提升数: {overview['性能提升数']} [成功]")
        print(f"   性能稳定数: {overview['性能稳定数']} [稳定]")
        
        # 回归统计
        regression_stats = report["回归统计"]
        if sum(regression_stats.values()) > 0:
            print(f"\n回归严重程度分布:")
            for severity, count in regression_stats.items():
                if count > 0:
                    icon = {"致命": "[致命]", "严重": "[严重]", "中等": "[中等]", "轻微": "[轻微]"}.get(severity, "[其他]")
                    print(f"   {icon} {severity}: {count} 个")
        
        # 详细结果
        print(f"\n详细检测结果:")
        for result in report["详细结果"]:
            severity_icon = {"致命": "[致命]", "严重": "[严重]", "中等": "[中等]", "轻微": "[轻微]"}.get(result["severity"], "[其他]")
            status_icon = "[失败]" if result["is_regression"] else "[成功]"
            
            print(f"   {status_icon} {result['message']}")
            if result["is_regression"] and result["recommendations"]:
                for rec in result["recommendations"][:2]:  # 只显示前2个建议
                    print(f"      • {rec}")
        
        # 整体建议
        print(f"\n建议措施:")
        for rec in report["建议措施"]:
            print(f"   • {rec}")
        
        print("="*60)


def create_detector_from_test_results(test_results: Dict[str, Any]) -> PerformanceRegressionDetector:
    """从测试结果创建回归检测器"""
    detector = PerformanceRegressionDetector()
    
    # 从测试结果中提取性能指标
    for test_name, result in test_results.items():
        if isinstance(result, dict) and "avg_time" in result:
            detector.add_metric(
                name=f"{test_name}_avg_time",
                value=result["avg_time"] * 1000,  # 转换为毫秒
                unit="ms"
            )
            
            if "max_time" in result:
                detector.add_metric(
                    name=f"{test_name}_max_time",
                    value=result["max_time"] * 1000,
                    unit="ms"
                )
    
    return detector


if __name__ == "__main__":
    # 演示性能回归检测
    print("性能回归检测器演示")
    
    detector = PerformanceRegressionDetector("demo_baseline.json")
    
    # 模拟当前性能数据
    detector.add_metrics_batch({
        "api_response_time": 120,      # 120ms
        "database_query_time": 45,     # 45ms
        "cache_hit_time": 2,           # 2ms
        "memory_usage": 150,           # 150MB
    })
    
    # 如果没有基准，先设置基准
    if not detector.baseline_metrics:
        print("设置基准数据...")
        detector.set_baseline()
    else:
        # 检测回归
        print("检测性能回归...")
        detector.print_report()
    
    print("演示完成")