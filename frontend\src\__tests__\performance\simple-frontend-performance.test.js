/**
 * 简化的前端性能测试
 * 专注于核心性能指标验证
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'

// 性能测试工具类
class FrontendPerformanceUtils {
  constructor() {
    this.measurements = {}
  }

  // 测量函数执行时间
  async measureTime(name, fn) {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    const duration = end - start
    
    this.measurements[name] = {
      duration,
      timestamp: new Date().toISOString()
    }
    
    return { result, duration }
  }

  // 测量内存使用
  measureMemory(name) {
    if (performance.memory) {
      this.measurements[`${name}_memory`] = {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
        timestamp: new Date().toISOString()
      }
    }
  }

  // 生成测试数据
  generateTestData(count = 100) {
    const data = []
    for (let i = 0; i < count; i++) {
      data.push({
        id: i + 1,
        rule_id: `perf_test_${i.toString().padStart(6, '0')}`,
        rule_key: 'performance_test_rule',
        rule_name: `性能测试规则${i}`,
        level1: '一级错误类型',
        level2: '二级错误类型',
        level3: '三级错误类型',
        error_reason: `性能测试错误原因${i}`,
        degree: ['轻微', '一般', '严重', '致命'][i % 4],
        reference: '性能测试参考资料',
        detail_position: '测试位置',
        prompted_fields1: `TEST${(i + 1000).toString()}`,
        type: '性能测试',
        pos: '测试业务',
        applicableArea: ['全国', '北京', '上海', '广州'][i % 4],
        default_use: i % 2 === 0 ? '是' : '否',
        start_date: '2025-01-01',
        end_date: '2025-12-31',
        status: 'ACTIVE',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    }
    return data
  }

  // 获取性能报告
  getPerformanceReport() {
    return {
      measurements: this.measurements,
      summary: this.generateSummary()
    }
  }

  generateSummary() {
    const timeMeasurements = Object.entries(this.measurements)
      .filter(([key]) => !key.includes('_memory'))
      .map(([key, value]) => ({ name: key, duration: value.duration }))

    const memoryMeasurements = Object.entries(this.measurements)
      .filter(([key]) => key.includes('_memory'))
      .map(([key, value]) => ({ name: key, ...value }))

    return {
      averageTime: timeMeasurements.length > 0 
        ? timeMeasurements.reduce((sum, m) => sum + m.duration, 0) / timeMeasurements.length 
        : 0,
      slowestOperation: timeMeasurements.length > 0 
        ? timeMeasurements.reduce((max, m) => m.duration > max.duration ? m : max)
        : null,
      memoryUsage: memoryMeasurements
    }
  }
}

describe('简化前端性能测试', () => {
  let performanceUtils
  let pinia

  beforeEach(() => {
    performanceUtils = new FrontendPerformanceUtils()
    pinia = createPinia()
    setActivePinia(pinia)
    
    // Mock fetch API
    global.fetch = vi.fn()
  })

  describe('数据处理性能测试', () => {
    it('应该快速生成测试数据', async () => {
      const { duration } = await performanceUtils.measureTime('data_generation', async () => {
        return performanceUtils.generateTestData(1000)
      })

      // 数据生成应在50ms内完成
      expect(duration).toBeLessThan(50)
      console.log(`✅ 数据生成性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效处理数据转换', async () => {
      const testData = performanceUtils.generateTestData(1000)

      const { duration } = await performanceUtils.measureTime('data_transformation', async () => {
        return testData.map(item => ({
          id: item.id,
          name: item.rule_name.toUpperCase(),
          category: `${item.level1}-${item.level2}`,
          severity: item.degree,
          active: item.status === 'ACTIVE'
        }))
      })

      // 数据转换应在30ms内完成
      expect(duration).toBeLessThan(30)
      console.log(`✅ 数据转换性能: ${duration.toFixed(2)}ms`)
    })

    it('应该快速执行数据过滤', async () => {
      const testData = performanceUtils.generateTestData(5000)

      const { duration } = await performanceUtils.measureTime('data_filtering', async () => {
        return testData.filter(item => 
          item.degree === '严重' && 
          item.default_use === '是' &&
          item.rule_name.includes('测试')
        )
      })

      // 数据过滤应在20ms内完成
      expect(duration).toBeLessThan(20)
      console.log(`✅ 数据过滤性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效执行数据排序', async () => {
      const testData = performanceUtils.generateTestData(2000)

      const { duration } = await performanceUtils.measureTime('data_sorting', async () => {
        return testData.sort((a, b) => {
          // 多字段排序
          if (a.degree !== b.degree) {
            const severityOrder = { '致命': 4, '严重': 3, '一般': 2, '轻微': 1 }
            return severityOrder[b.degree] - severityOrder[a.degree]
          }
          return a.rule_name.localeCompare(b.rule_name)
        })
      })

      // 数据排序应在50ms内完成
      expect(duration).toBeLessThan(50)
      console.log(`✅ 数据排序性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('状态管理性能测试', () => {
    it('应该快速创建和更新Pinia store', async () => {
      const { duration } = await performanceUtils.measureTime('store_operations', async () => {
        // 模拟store操作
        const store = pinia._s.get('ruleDetails') || { 
          state: { 
            rules: [], 
            loading: false, 
            total: 0 
          },
          actions: {
            setRules: (rules) => { store.state.rules = rules },
            setLoading: (loading) => { store.state.loading = loading },
            setTotal: (total) => { store.state.total = total }
          }
        }

        // 执行多次状态更新
        for (let i = 0; i < 100; i++) {
          store.actions.setLoading(true)
          store.actions.setRules(performanceUtils.generateTestData(10))
          store.actions.setTotal(10)
          store.actions.setLoading(false)
        }

        return store
      })

      // Store操作应在100ms内完成
      expect(duration).toBeLessThan(100)
      console.log(`✅ Store操作性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('API模拟性能测试', () => {
    it('应该快速处理API响应', async () => {
      const testData = performanceUtils.generateTestData(100)
      
      // Mock API响应
      global.fetch.mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: {
            items: testData,
            total: testData.length,
            page: 1,
            page_size: 100
          }
        })
      })

      const { duration } = await performanceUtils.measureTime('api_processing', async () => {
        const response = await fetch('/api/test')
        const data = await response.json()
        
        // 模拟数据处理
        return data.data.items.map(item => ({
          ...item,
          displayName: `${item.rule_name} (${item.degree})`
        }))
      })

      // API处理应在50ms内完成
      expect(duration).toBeLessThan(50)
      console.log(`✅ API处理性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效处理并发API调用', async () => {
      // Mock多个API响应
      for (let i = 0; i < 5; i++) {
        global.fetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            success: true,
            data: performanceUtils.generateTestData(50)
          })
        })
      }

      const { duration } = await performanceUtils.measureTime('concurrent_api_calls', async () => {
        const promises = []
        for (let i = 0; i < 5; i++) {
          promises.push(
            fetch(`/api/test/${i}`).then(res => res.json())
          )
        }
        return await Promise.all(promises)
      })

      // 并发API调用应在200ms内完成
      expect(duration).toBeLessThan(200)
      console.log(`✅ 并发API调用性能: ${duration.toFixed(2)}ms`)
    })
  })

  describe('内存使用测试', () => {
    it('应该有效管理内存使用', async () => {
      if (!performance.memory) {
        console.log('⚠️ 浏览器不支持内存监控，跳过内存测试')
        return
      }

      performanceUtils.measureMemory('initial')

      // 创建大量数据
      const largeDataSets = []
      for (let i = 0; i < 10; i++) {
        largeDataSets.push(performanceUtils.generateTestData(1000))
      }

      performanceUtils.measureMemory('after_creation')

      // 清理数据
      largeDataSets.length = 0

      // 强制垃圾回收（如果支持）
      if (global.gc) {
        global.gc()
      }

      performanceUtils.measureMemory('after_cleanup')

      const report = performanceUtils.getPerformanceReport()
      console.log('📊 内存使用报告:', report.summary.memoryUsage)

      // 验证内存使用合理
      const initial = report.summary.memoryUsage.find(m => m.name === 'initial_memory')
      const afterCleanup = report.summary.memoryUsage.find(m => m.name === 'after_cleanup_memory')

      if (initial && afterCleanup) {
        const memoryIncrease = afterCleanup.usedJSHeapSize - initial.usedJSHeapSize
        const memoryIncreasePercent = (memoryIncrease / initial.usedJSHeapSize) * 100

        // 内存增长不应超过30%
        expect(memoryIncreasePercent).toBeLessThan(30)
        console.log(`✅ 内存使用测试通过，增长: ${memoryIncreasePercent.toFixed(2)}%`)
      }
    })
  })

  describe('算法性能测试', () => {
    it('应该快速执行搜索算法', async () => {
      const testData = performanceUtils.generateTestData(10000)
      const searchTerm = '性能测试规则5000'

      const { duration } = await performanceUtils.measureTime('search_algorithm', async () => {
        // 线性搜索
        return testData.find(item => item.rule_name === searchTerm)
      })

      // 搜索应在10ms内完成
      expect(duration).toBeLessThan(10)
      console.log(`✅ 搜索算法性能: ${duration.toFixed(2)}ms`)
    })

    it('应该高效执行分页算法', async () => {
      const testData = performanceUtils.generateTestData(10000)
      const pageSize = 50
      const pageNumber = 100

      const { duration } = await performanceUtils.measureTime('pagination_algorithm', async () => {
        const startIndex = (pageNumber - 1) * pageSize
        const endIndex = startIndex + pageSize
        return testData.slice(startIndex, endIndex)
      })

      // 分页应在5ms内完成
      expect(duration).toBeLessThan(5)
      console.log(`✅ 分页算法性能: ${duration.toFixed(2)}ms`)
    })
  })

  afterAll(() => {
    // 输出完整的性能报告
    const report = performanceUtils.getPerformanceReport()
    console.log('\n📊 前端性能测试完整报告:')
    console.log('平均执行时间:', report.summary.averageTime.toFixed(2), 'ms')
    if (report.summary.slowestOperation) {
      console.log('最慢操作:', report.summary.slowestOperation.name, '-', report.summary.slowestOperation.duration.toFixed(2), 'ms')
    }
    
    // 输出详细测量数据
    console.log('\n详细性能数据:')
    Object.entries(report.measurements).forEach(([name, data]) => {
      if (!name.includes('_memory')) {
        console.log(`  ${name}: ${data.duration.toFixed(2)}ms`)
      }
    })
  })
})
