"""
测试数据管理器
用于统一管理性能测试中的测试数据生成、存储、清理等操作
"""

import os
import json
import time
import uuid
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from contextlib import contextmanager


@dataclass
class TestDataInfo:
    """测试数据信息"""
    data_id: str
    data_type: str
    count: int
    created_at: float
    rule_key: str
    cleanup_required: bool = True
    metadata: Dict[str, Any] = None


class TestDataManager:
    """测试数据管理器"""
    
    def __init__(self, cleanup_on_exit: bool = True):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.cleanup_on_exit = cleanup_on_exit
        self.created_data = []
        self.cleanup_callbacks = []
        
        # 自动清理设置
        if cleanup_on_exit:
            import atexit
            atexit.register(self.cleanup_all)
    
    def generate_rule_details(self, count: int, rule_key: str = "performance_test_rule", 
                             prefix: str = "perf_test") -> List[Dict[str, Any]]:
        """生成规则明细测试数据"""
        data_id = f"{prefix}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        details = []
        for i in range(count):
            detail = {
                "rule_id": f"{data_id}_{i:06d}",
                "rule_key": rule_key,
                "rule_name": f"性能测试规则{i}",
                "level1": "一级错误类型",
                "level2": "二级错误类型", 
                "level3": "三级错误类型",
                "error_reason": f"性能测试错误原因{i}",
                "degree": ["轻微", "一般", "严重", "致命"][i % 4],
                "reference": "性能测试参考资料",
                "detail_position": "测试位置",
                "prompted_fields1": f"TEST{(i + 1000):04d}",
                "type": "性能测试",
                "pos": "测试业务",
                "applicableArea": ["全国", "北京", "上海", "广州"][i % 4],
                "default_use": "是" if i % 2 == 0 else "否",
                "start_date": "2025-01-01",
                "end_date": "2025-12-31",
                "status": "ACTIVE",
                "extended_fields": {
                    "test_field_1": f"测试值{i}",
                    "test_field_2": i % 100,
                    "test_field_3": i % 2 == 0,
                    "batch_id": data_id
                }
            }
            details.append(detail)
        
        # 记录数据信息
        data_info = TestDataInfo(
            data_id=data_id,
            data_type="rule_details",
            count=count,
            created_at=time.time(),
            rule_key=rule_key,
            metadata={"prefix": prefix, "generated": True}
        )
        self.created_data.append(data_info)
        
        self.logger.info(f"生成测试数据：{count}条规则明细，ID: {data_id}")
        return details
    
    def generate_test_template(self, rule_key: str = "performance_test_rule") -> Dict[str, Any]:
        """生成测试规则模板"""
        template = {
            "rule_key": rule_key,
            "rule_type": "性能测试规则",
            "name": "性能测试规则模板",
            "description": "用于性能测试的规则模板",
            "status": "READY",
            "created_at": time.time()
        }
        
        # 记录模板信息
        data_info = TestDataInfo(
            data_id=rule_key,
            data_type="rule_template",
            count=1,
            created_at=time.time(),
            rule_key=rule_key,
            metadata={"type": "template"}
        )
        self.created_data.append(data_info)
        
        return template
    
    def create_test_datasets(self, sizes: Dict[str, int] = None) -> Dict[str, List[Dict[str, Any]]]:
        """创建多种规模的测试数据集"""
        if sizes is None:
            sizes = {
                "small": 100,
                "medium": 1000, 
                "large": 10000
            }
        
        datasets = {}
        for name, size in sizes.items():
            datasets[f"{name}_dataset"] = self.generate_rule_details(
                count=size,
                prefix=f"{name}_perf"
            )
        
        # 添加测试模板
        datasets["test_template"] = self.generate_test_template()
        
        return datasets
    
    def register_cleanup_callback(self, callback_func, *args, **kwargs):
        """注册清理回调函数"""
        self.cleanup_callbacks.append((callback_func, args, kwargs))
    
    def save_test_data(self, data: Dict[str, Any], filename: str) -> str:
        """保存测试数据到文件"""
        filepath = os.path.join("/tmp", filename) if os.name != 'nt' else os.path.join(os.getenv('TEMP', '.'), filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 注册文件清理
            self.register_cleanup_callback(self._cleanup_file, filepath)
            self.logger.info(f"测试数据已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存测试数据失败: {e}")
            raise
    
    def _cleanup_file(self, filepath: str):
        """清理文件"""
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
                self.logger.debug(f"已清理文件: {filepath}")
        except Exception as e:
            self.logger.warning(f"清理文件失败 {filepath}: {e}")
    
    def cleanup_all(self):
        """清理所有资源"""
        cleaned_count = 0
        
        try:
            # 执行清理回调
            for callback, args, kwargs in self.cleanup_callbacks:
                try:
                    callback(*args, **kwargs)
                    cleaned_count += 1
                except Exception as e:
                    self.logger.warning(f"清理回调执行失败: {e}")
            
            # 清理数据记录
            self.created_data.clear()
            self.cleanup_callbacks.clear()
            
            # 强制垃圾回收
            import gc
            gc.collect()
            
            self.logger.info(f"测试数据管理器清理完成：处理了 {cleaned_count} 项资源")
            
        except Exception as e:
            self.logger.error(f"清理过程中发生错误: {e}")
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        total_items = sum(data.count for data in self.created_data)
        data_types = {}
        
        for data in self.created_data:
            data_type = data.data_type
            if data_type not in data_types:
                data_types[data_type] = {"count": 0, "items": 0}
            data_types[data_type]["count"] += 1
            data_types[data_type]["items"] += data.count
        
        return {
            "total_datasets": len(self.created_data),
            "total_items": total_items,
            "data_types": data_types,
            "cleanup_callbacks": len(self.cleanup_callbacks)
        }
    
    @contextmanager
    def temporary_data(self, count: int, rule_key: str = "temp_test_rule"):
        """临时数据上下文管理器"""
        data = self.generate_rule_details(count, rule_key, prefix="temp")
        try:
            yield data
        finally:
            # 这里可以添加特定的临时数据清理逻辑
            pass


# 工具函数
def create_performance_test_data(manager: TestDataManager = None) -> Dict[str, Any]:
    """创建标准的性能测试数据"""
    if manager is None:
        manager = TestDataManager()
    
    return manager.create_test_datasets({
        "small": 100,
        "medium": 1000,
        "large": 5000,
        "extra_large": 10000
    })


def cleanup_test_environment():
    """清理测试环境"""
    import gc
    import psutil
    
    # 强制垃圾回收
    gc.collect()
    
    # 获取当前进程内存信息
    process = psutil.Process()
    memory_info = process.memory_info()
    
    print(f"内存清理后状态: RSS={memory_info.rss/1024/1024:.1f}MB")


if __name__ == "__main__":
    # 测试数据管理器
    print("测试数据管理器演示")
    
    manager = TestDataManager()
    
    # 生成测试数据
    datasets = create_performance_test_data(manager)
    
    # 显示摘要
    summary = manager.get_data_summary()
    print(f"数据摘要: {summary}")
    
    # 测试临时数据
    with manager.temporary_data(50) as temp_data:
        print(f"临时数据: {len(temp_data)} 条")
    
    print("测试数据管理器演示完成")